spring.application.name=payment-service
security.basic.enable=false
server.port=${PAYMENT_PORT:${SERVER_PORT:8091}}
info.app.description=Project for executing different types of payments
info.app.version=${BUILD_REVISION:1.0.0}
spring.datasource.hikari.connection-timeout=${DB_TIMEOUT:20000}
spring.datasource.hikari.minimum-idle=${DB_MIN_IDLE:2}
spring.datasource.hikari.idle-timeout=${DB_IDLE_TIME:300000}
spring.datasource.hikari.max-lifetime=${DB_MAX_LIFE:0}
spring.datasource.hikari.maximum-pool-size=${DB_POOL:50}
spring.datasource.hikari.auto-commit=true
spring.datasource.testWhileIdle=true
spring.datasource.validationQuery=SELECT 1
spring.jpa.hibernate.ddl-auto=none
spring.jpa.generate-ddl=false
spring.jpa.open-in-view=false
spring.jpa.hibernate.naming-strategy=org.hibernate.cfg.ImprovedNamingStrategy
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS=1000
default.time.zone=${TIMEZONE:Asia/Kolkata}
#entitymanager.packagesToScan =com
logging.level.root=info
logging.level.com.seera.lumi.ultra.payment.api=DEBUG
logging.level.com.seera.lumi.ultra.payment.utils.CustomLoggingFilter=DEBUG
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG
logging.level.com.checkout=DEBUG

#SQL Logging
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=true
#logging.level.org.hibernate.SQL=DEBUG
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

server.compression.enabled=true
server.compression.mime-types=text/html, text/xml, text/plain, text/css, text/javascript, application/javascript, application/json
spring.profiles.active=${SPRING_PROFILES_ACTIVE:local}
spring.redis.host=${SPRING_REDIS_HOST:localhost}
spring.redis.port=${SPRING_REDIS_PORT:6379}
spring.redis.password=${SPRING_REDIS_PASSWORD:}
spring.redis.ssl=${SPRING_REDIS_SSL:false}
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
feign.circuitbreaker.enabled=true
feign.client.config.default.connect-timeout=30000
feign.client.config.default.read-timeout=30000
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=${service.timeout:30000}
hystrix.command.default.execution.isolation.strategy=SEMAPHORE
#Swagger Config
springdoc.swagger-ui.path=/swagger-ui.html
swagger-context=/payment-service
#Monitoring
management.endpoints.web.exposure.include=health,info,prometheus
#Checkout
checkout.base.url=${CHECKOUT_URL:https://api.sandbox.checkout.com}
checkout.auth.sandbox=${CHECKOUT_SANDBOX:true}
# checkout v1
checkout.auth.token=${CHECKOUT_TOKEN:sk_test_3e4c5201-ae94-4eda-afe7-9065d94cf6ec}
checkout.auth.public.key=${CHECKOUT_PUBLIC_KEY:pk_test_68e68531-7fc1-440d-a101-1309a4958848}
checkout.auth.secret.key=${CHECKOUT_SECRET_KEY:sk_test_3e4c5201-ae94-4eda-afe7-9065d94cf6ec}
# checkout v2
checkout.auth.token.v2=${CHECKOUT_TOKEN_V2:sk_sbox_rd77g4t3cqnz3hhb7kpplcpzuex}
checkout.auth.public.key.v2=${CHECKOUT_PUBLIC_KEY_V2:pk_sbox_h7tokdlba57ryv46leprsra6hy7}
checkout.auth.secret.key.v2=${CHECKOUT_SECRET_KEY_V2:sk_sbox_rd77g4t3cqnz3hhb7kpplcpzuex}
checkout.auth.processing.channel.v2=${CHECKOUT_PROCESSING_CHANNEL_V2:pc_6iwtf7jqwqxe7ojwahnhttdfjq}
# Default Bin Numbers
bin.list.mada=538530,588845,440647,440795,446404,457865,968208,588846,493428,968210,636120,417633,468540,468541,468542,468543,968201,446393,588847,400861,409201,458456,484783,968205,462220,455708,588848,455036,968203,486094,486095,486096,504300,440533,489317,489318,489319,445564,968211,401757,431361,604906,521076,968202,529415,535825,543085,524130,554180,549760,588849,968209,524514,529741,537767,535989,536023,513213,585265,588983,588982,589005,508160,531095,530906,532013,588851,605141,968204,422817,422818,422819,428331,483010,483011,483012,589206,968207,419593,439956,439954,432328,428671,428672,428673,968206,446672,543357,434107,539931,588850,558848,557606,410685
bin.list.3DS=538530,400293,454347,448504,588845,440647,440795,446404,457865,968208,588846,493428,968210,636120,417633,468540,468541,468542,468543,968201,446393,588847,400861,409201,458456,484783,968205,462220,455708,588848,455036,968203,486094,486095,486096,504300,440533,489317,489318,489319,445564,968211,401757,431361,604906,521076,968202,529415,535825,543085,524130,554180,549760,588849,968209,524514,529741,537767,535989,536023,513213,585265,588983,588982,589005,508160,531095,530906,532013,588851,605141,968204,422817,422818,422819,428331,483010,483011,483012,589206,968207,419593,439956,439954,450778,496649,454683,485005,428375,433988,520090,454335,456893,412113,540902,427222,443913,452335,540236,432328,428671,428672,428673,968206,446672,543357,434107,539931,588850,558848,557606,410685,473826,523970,514932,552075,532448,540236,552360,374311,374312,376655,376656,377314,377347,379334,523970,552250,528479,543199,544873,440532,456891,471388,539830,433987,428969,426611,417323,544152,457268,466515,414627
#Kafka settings
kafka.listen.auto.start=true
kafka.dlt.listen.auto.start=${DLT_AUTO_START:true}
kafka.listen.concurrency=${KAFKA_LISTENER_CONCURRENCY:1}
spring.kafka.bootstrap-servers=${KAFKA_HOST:localhost:9092}
spring.cloud.stream.kafka.binder.minPartitionCount=${KAFKA_PARTITION:1}
spring.cloud.stream.kafka.binder.replicationFactor=${KAFKA_REPLICATION:1}
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.group-id=${spring.application.name}
kafka.topic.checkout.webhook.event=CheckoutWebhookEvent
kafka.topic.payment.event=PaymentEvent
kafka.topic.customer.card.event=CustomerCardEvent
kafka.topic.quickpay.payment.event=QuickPayPaymentEvent
kafka.topic.request.refund.event=SmsEvent
kafka.topic.yaqeen.agreement.started.event=YaqeenAgreementStartedEvent
kafka.topic.sap.payment.notification.event=SAPPaymentNotificationEvent

# Liquibase
spring.liquibase.enabled=true
spring.liquibase.contexts=dev,qa,uat,prod
spring.liquibase.change-log=classpath:/db/migration/changelog-master.xml
spring.datasource.url=${SPRING_READ_REPLICA_DATASOURCE_URL:***********************************************************************************************************************************************************************************************************************}
spring.datasource.username=${SPRING_READ_REPLICA_DATASOURCE_USERNAME:nitin.maurya}
spring.datasource.password=${SPRING_READ_REPLICA_DATASOURCE_PASSWORD:52zYuBHvct49dq2Pm9yN}


driver=com.mysql.jdbc.Driver
outputChangeLogFile=src/main/resources/db/changes/initial-change-log.xml
referenceUrl=*********************************************
referenceUsername=
referencePassword=
diffChangeLogFile=src/main/resources/db/changes/diff.xml
changeLogFile=src/main/resources/db/migration/changelog-master.xml
#Command:mvn liquibase:generateChangeLog
#Command:mvn liquibase:diff


#Apple Pay Configuration
apple.pay.config.ssl-certificate-url=${APPLE_PAY_SSL_CERTIFICATE_URL:}
apple.pay.config.ssl-certificate-password=${APPLE_PAY_SSL_CERTIFICATE_PASSWORD:}
apple.pay.config.merchant-identifier=${APPLE_PAY_MERCHANT_IDENTIFIER:}
apple.pay.config.initiative-context=${APPLE_PAY_INITIATIVE_CONTEXT:}
apple.pay.config.initiative=${APPLE_PAY_INITIATIVE:}
apple.pay.config.display-name=${APPLE_PAY_DISPLAY_NAME:}

jwt.auth.token.secret=${JWT_AUTH_TOKEN_SECRET:}
jwt.auth.token.validity.hours=${JWT_AUTH_TOKEN_VALIDITY_HOURS:4320}

quickpay.redirect.url=${QUICKPAY_LUMI_REDIRECT_URL:https://dev.lumirental.com/{language}/payment/quickpay/{trackId}?type={payType}}
refund.redirect.url=${REFUND_LUMI_REDIRECT_URL:https://dev.lumirental.com/ar/refund?userToken=%s&trackId=%s}

# Keycloak Config Resolver paths
logging.level.org.keycloak=${KEYCLOAK_LOG_LEVEL:INFO}
config.keycloak.config-path=${KEYCLOAK_CONFIG_PATH:src/main/resources/keycloak-lumi.json}
config.keycloak.policy-enforcer-path=${KEYCLOAK_POLICY_ENFORCER_CONFIG_PATH:src/main/resources/policy-enforcer-lumi.json}

#API
api.agreement.service.base.url=${AGREEMENT_SERVICE:}
api.branch.service.base.url=${BRANCH_SERVICE:https://api-dev.lumirental.com/branch-service}
api.user.service.base.url=${USER_SERVICE:https://api-dev.lumirental.com/core-user-service}
api.integration.adapter.service.base.url=${INTEGRATION_ADAPTOR_SERVICE:https://api-dev.lumirental.com/core-integrations-adapter-service}

google.appcheck.enabled=${APPCHECK_VERIFICATION_ENABLED:true}
google.appcheck.jwksProviderUrl=${APPCHECK_JWKS_PROVIDER_URL:https://firebaseappcheck.googleapis.com/v1beta/jwks}
google.appcheck.issuerBaseUrl=${APPCHECK_ISSUER_BASE_URL:https://firebaseappcheck.googleapis.com/%s}
google.appcheck.issuerProjectId=${APPCHECK_ISSUER_PROJECT_ID:56818609648}
google.appcheck.verificationAudience=${APPCHECK_VERIFICATION_AUDIENCE:projects/%s}
google.appcheck.availablePlatforms=${APPCHECK_AVAILABLE_PLATFORMS:ios}