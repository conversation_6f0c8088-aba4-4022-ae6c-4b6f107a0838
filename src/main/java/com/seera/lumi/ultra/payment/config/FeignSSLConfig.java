package com.seera.lumi.ultra.payment.config;

import com.seera.lumi.ultra.payment.utils.FeignSSLUtils;
import feign.Client;
import feign.Feign;
import feign.Logger;
import feign.Retryer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

public class FeignSSLConfig {

  @Value("${apple.pay.config.ssl-certificate-url}")
  private String sslCertificateUrl;

  @Value("${apple.pay.config.ssl-certificate-password}")
  private String sslCertificatePassword;

//  @Bean (name = "feignSSLClient")
//  public Client feignSSLClient() throws Exception {
//    return FeignSSLUtils.getFeignClient(sslCertificateUrl, sslCertificatePassword);
//  }

  @Bean
  public Retryer feignRetryer() {
    return new Retryer.Default(100L, 200L, 2);
  }

  @Bean
  public Feign.Builder feignBuilder() {
    return Feign.builder().logLevel(Logger.Level.FULL);
  }
}
